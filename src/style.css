@import 'tailwindcss';

@plugin "daisyui" {
	themes: false;
	exclude: rootscrollgutter;
}

@plugin "daisyui/theme" {
	name: 'dark';
	default: true;
	prefersdark: true;
	color-scheme: 'dark';
	/* Base colors - Clean dark foundation with blue undertones */
	--color-base-100: oklch(11% 0.015 240);
	/* Deep dark with subtle blue hint */
	--color-base-200: oklch(15% 0.02 240);
	/* Subtle elevation with blue warmth */
	--color-base-300: oklch(19% 0.025 240);
	/* Borders and dividers with blue tint */
	--color-base-content: oklch(96% 0.008 240);
	/* High contrast text with blue undertone */

	/* Primary - Beautiful vibrant blue */
	--color-primary: oklch(68% 0.19 240);
	/* Stunning electric blue */
	--color-primary-content: oklch(12% 0.02 240);

	/* Secondary - Complementary cyan-teal */
	--color-secondary: oklch(72% 0.16 200);
	--color-secondary-content: oklch(12% 0.02 200);

	/* Accent - Elegant purple-indigo */
	--color-accent: oklch(65% 0.18 280);
	--color-accent-content: oklch(96% 0.008 280);

	/* Neutral - Consistent with base blue theme */
	--color-neutral: oklch(17% 0.02 240);
	--color-neutral-content: oklch(96% 0.008 240);

	/* Semantic colors - Clear and distinct with blue harmony */
	--color-info: oklch(70% 0.17 220);
	--color-info-content: oklch(12% 0.02 220);

	--color-success: oklch(68% 0.16 160);
	/* Harmonious green that complements blue */
	--color-success-content: oklch(12% 0.02 160);

	--color-warning: oklch(75% 0.18 60);
	--color-warning-content: oklch(12% 0.02 60);

	--color-error: oklch(68% 0.22 15);
	--color-error-content: oklch(96% 0.008 15);

	/* UI Properties - Modern and clean */
	--radius-selector: 0.5rem;
	--radius-field: 0.375rem;
	--radius-box: 0.75rem;
	--size-selector: 0.25rem;
	--size-field: 0.25rem;
	--border: 1px;
}

@plugin "daisyui/theme" {
	name: 'light';
	default: false;
	prefersdark: false;
	color-scheme: 'light';
	/* Base colors - Clean light foundation with blue undertones */
	--color-base-100: oklch(99% 0.008 240);
	/* Pure white with subtle blue hint */
	--color-base-200: oklch(96% 0.012 240);
	/* Subtle elevation with blue warmth */
	--color-base-300: oklch(90% 0.018 240);
	/* Borders and dividers with blue tint */
	--color-base-content: oklch(18% 0.025 240);
	/* Dark text with blue undertone */

	/* Primary - Rich professional blue for light mode */
	--color-primary: oklch(55% 0.22 240);
	/* Deep professional blue with perfect contrast */
	--color-primary-content: oklch(98% 0.008 240);

	/* Secondary - Complementary cyan-teal */
	--color-secondary: oklch(52% 0.18 200);
	--color-secondary-content: oklch(98% 0.008 200);

	/* Accent - Elegant purple-indigo */
	--color-accent: oklch(50% 0.2 280);
	--color-accent-content: oklch(98% 0.008 280);

	/* Neutral - Consistent with base blue theme */
	--color-neutral: oklch(88% 0.018 240);
	--color-neutral-content: oklch(18% 0.025 240);

	/* Semantic colors - Clear and accessible with blue harmony */
	--color-info: oklch(52% 0.2 220);
	--color-info-content: oklch(98% 0.008 220);

	--color-success: oklch(48% 0.18 160);
	/* Harmonious green that complements blue */
	--color-success-content: oklch(98% 0.008 160);

	--color-warning: oklch(65% 0.2 60);
	--color-warning-content: oklch(98% 0.008 60);

	--color-error: oklch(55% 0.24 15);
	--color-error-content: oklch(98% 0.008 15);

	/* UI Properties - Modern and clean */
	--radius-selector: 0.5rem;
	--radius-field: 0.375rem;
	--radius-box: 0.75rem;
	--size-selector: 0.25rem;
	--size-field: 0.25rem;
	--border: 1px;
}

.input,
.textarea,
.select {
	&:focus,
	&:focus-within {
		outline-color: color-mix(in oklch, var(--color-primary) 40%, transparent);
		border-color: var(--color-primary);
		transition: all 0.2s ease;
	}
}

/* Card gradient styles - Clean and modern */
.card-gradient-primary {
	background: color-mix(in oklch, var(--color-primary) 10%, var(--color-base-100));
	border: 1px solid color-mix(in oklch, var(--color-primary) 20%, transparent);
	transition: transform 0.2s ease;
}

.card-gradient-primary:hover {
	transform: translateY(-2px);
}

.card-gradient-secondary {
	background: color-mix(in oklch, var(--color-secondary) 10%, var(--color-base-100));
	border: 1px solid color-mix(in oklch, var(--color-secondary) 20%, transparent);
	transition: transform 0.2s ease;
}

.card-gradient-secondary:hover {
	transform: translateY(-2px);
}

.card-gradient-accent {
	background: color-mix(in oklch, var(--color-accent) 10%, var(--color-base-100));
	border: 1px solid color-mix(in oklch, var(--color-accent) 20%, transparent);
	transition: transform 0.2s ease;
}

.card-gradient-accent:hover {
	transform: translateY(-2px);
}

.card-gradient-info {
	background: color-mix(in oklch, var(--color-info) 10%, var(--color-base-100));
	border: 1px solid color-mix(in oklch, var(--color-info) 20%, transparent);
	transition: transform 0.2s ease;
}

.card-gradient-info:hover {
	transform: translateY(-2px);
}

.card-gradient-neutral {
	background: var(--color-base-200);
	border: 1px solid var(--color-base-300);
	transition: transform 0.2s ease;
}

.card-gradient-neutral:hover {
	transform: translateY(-2px);
}

/* Icon containers - Clean and simple */
.icon-container {
	padding: 0.75rem;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-container-primary {
	background-color: color-mix(in oklch, var(--color-primary) 15%, transparent);
	color: var(--color-primary);
}

.icon-container-secondary {
	background-color: color-mix(in oklch, var(--color-secondary) 15%, transparent);
	color: var(--color-secondary);
}

.icon-container-accent {
	background-color: color-mix(in oklch, var(--color-accent) 15%, transparent);
	color: var(--color-accent);
}

.icon-container-info {
	background-color: color-mix(in oklch, var(--color-info) 15%, transparent);
	color: var(--color-info);
}

/* Modern fieldset styling */
.fieldset {
	padding: 1rem;
	border-radius: var(--radius-box);
	border: 1px solid var(--color-base-300);
}

.fieldset > div {
	margin: 0.5rem;
}

.input-hint {
	font-size: 0.875rem;
	opacity: 0.7;
	margin-top: 0.25rem;
	color: var(--color-base-content);
}

/* Data display */
.data-display {
	padding: 1.5rem;
	background-color: var(--color-base-200);
	border-radius: var(--radius-box);
	margin-bottom: 1.5rem;
}

/* Empty states */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 3rem;
	background-color: var(--color-base-100);
	border-radius: var(--radius-box);
}

.empty-state-icon {
	color: color-mix(in oklch, var(--color-base-content) 40%, transparent);
	margin-bottom: 1rem;
}

.empty-state-title {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
	color: var(--color-base-content);
}

.empty-state-message {
	color: color-mix(in oklch, var(--color-base-content) 70%, transparent);
	font-size: 0.875rem;
}

/* Enhanced modal styling */

dialog.modal {
	backdrop-filter: blur(8px);
	background: color-mix(in oklch, var(--color-base-100) 70%, transparent);
}

dialog.modal .modal-box {
	border: 1px solid var(--color-base-300);
}

/* Smooth scrollbar styling */
::-webkit-scrollbar {
	width: 5px;
	height: 5px;
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	background: color-mix(in oklch, var(--color-base-content) 20%, transparent);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background: color-mix(in oklch, var(--color-base-content) 40%, transparent);
}
